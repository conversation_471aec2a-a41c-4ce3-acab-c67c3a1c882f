1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bktfindernative"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\debug\AndroidManifest.xml:5:5-77
11-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\debug\AndroidManifest.xml:5:22-75
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:3:5-67
12-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:3:22-64
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:4:5-79
13-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:4:22-76
14
15    <permission
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\370027f6a753b8b73f2db12bef842016\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.bktfindernative.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\370027f6a753b8b73f2db12bef842016\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\370027f6a753b8b73f2db12bef842016\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.bktfindernative.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\370027f6a753b8b73f2db12bef842016\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\370027f6a753b8b73f2db12bef842016\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:5:5-25:19
22        android:name="com.bktfindernative.MainApplication"
22-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:6:7-38
23        android:allowBackup="false"
23-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:10:7-34
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\370027f6a753b8b73f2db12bef842016\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
25        android:debuggable="true"
26        android:extractNativeLibs="true"
27        android:icon="@mipmap/ic_launcher"
27-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:8:7-41
28        android:label="@string/app_name"
28-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:7:7-39
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:9:7-52
30        android:theme="@style/AppTheme"
30-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:11:7-38
31        android:usesCleartextTraffic="true" >
31-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:12:7-42
32        <activity
32-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\debug\AndroidManifest.xml:11:9-111
33            android:name="com.facebook.react.devsupport.DevSettingsActivity"
33-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\debug\AndroidManifest.xml:11:19-83
34            android:exported="false" />
34-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\debug\AndroidManifest.xml:11:84-108
35        <activity
35-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:13:7-24:18
36            android:name="com.bktfindernative.MainActivity"
36-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:14:9-37
37            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
37-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:16:9-118
38            android:exported="true"
38-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:19:9-32
39            android:label="@string/app_name"
39-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:15:9-41
40            android:launchMode="singleTask"
40-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:17:9-40
41            android:windowSoftInputMode="adjustResize" >
41-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:18:9-51
42            <intent-filter>
42-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:20:9-23:25
43                <action android:name="android.intent.action.MAIN" />
43-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:21:13-65
43-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:21:21-62
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:22:13-73
45-->C:\maarifaprojeleri\bkt-finder-native-app\BktFinderNative\android\app\src\main\AndroidManifest.xml:22:23-70
46            </intent-filter>
47        </activity>
48
49        <provider
49-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52c4d84eb0fea478ac0a8482127f7ee9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
50            android:name="androidx.startup.InitializationProvider"
50-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52c4d84eb0fea478ac0a8482127f7ee9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
51            android:authorities="com.bktfindernative.androidx-startup"
51-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52c4d84eb0fea478ac0a8482127f7ee9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
52            android:exported="false" >
52-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52c4d84eb0fea478ac0a8482127f7ee9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
53            <meta-data
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52c4d84eb0fea478ac0a8482127f7ee9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.emoji2.text.EmojiCompatInitializer"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52c4d84eb0fea478ac0a8482127f7ee9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
55                android:value="androidx.startup" />
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\52c4d84eb0fea478ac0a8482127f7ee9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
56            <meta-data
56-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\7774995ae2a9abedc20efe505ad0ebe9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
57                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
57-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\7774995ae2a9abedc20efe505ad0ebe9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
58                android:value="androidx.startup" />
58-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\7774995ae2a9abedc20efe505ad0ebe9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
59        </provider>
60    </application>
61
62</manifest>
