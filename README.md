# Finder Native Mobile App (No Expo)



## Setting up environment

- [ ] Install node & npm via installer. https://nodejs.org/en/download/

- [ ] Install Yarn. 
```
npm install --global yarn
```

- [ ] Install node modules. 
```
yarn install
```

***
#### Dev Environment (https://finder.maarifalab.xyz/)

#### Production Environment (https://www.doraepare.com/)


***
inspect on chrome (make sure emulator listing  cmd>adb devices)
#### chrome://inspect/#devices

***
Add the following codes in "AndroidManifest.xml" where android/app/src/main
  ```
 <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
 ```

***


rebuild android
```
cd android
./gradlew clean
./gradlew cleanBuildCache
./gradlew bundleRelease

before needs keytool to generate keystore. Check the build.gradle file for signingConfigs > release 

```