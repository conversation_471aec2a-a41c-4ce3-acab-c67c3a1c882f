import React, {useEffect, useRef, useState} from 'react';
import {
  // ActivityIndicator,
  Dimensions,
  Share,
  StyleSheet,
  Alert,
  Platform,
  PermissionsAndroid,
  BackHandler,
  Linking,
} from 'react-native';
import WebView, {WebViewNavigation} from 'react-native-webview';
import Spinner from 'react-native-loading-spinner-overlay/lib';
import Geolocation from '@react-native-community/geolocation';

function App() {
  const [isLoading, setLoading] = useState(false);
  const webViewRef = useRef<any>();
  const [canGoBack, setCangoBack] = useState(false);

  useEffect(() => {
    const backAction = () => {
      console.log('canGoBack', canGoBack);
      if (canGoBack === true) {
        webViewRef.current.goBack();
      } else {
        Alert.alert('Hold on!', 'Exit?', [
          {
            text: 'Cancel',
            onPress: () => null,
            style: 'cancel',
          },
          {text: 'YES', onPress: () => BackHandler.exitApp()},
        ]);
      }
      return true;
    };
    BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', backAction);
    };
  }, [canGoBack]);

  const locationAccess = async (callback_tracker: string) => {
    console.log('locationAccess:' + callback_tracker);
    if (Platform.OS === 'ios') {
      getLocation(function (location: any) {
        var status = 'granted'; // TODO // cunku bilmiyorum.
        webViewRef.current.postMessage(
          JSON.stringify({
            key: callback_tracker,
            value: {location, status}
          }),
        );
      });
    } else {
      try {
        const status = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        if (status === PermissionsAndroid.RESULTS.GRANTED) {
          //To Check, If Permission is granted
          getLocation(function (location: any) {
            console.log('locv2', location);
            console.log('locv3',  JSON.stringify({
              key: callback_tracker,
              value: {location, status},
            }),);
            webViewRef.current.postMessage(
              JSON.stringify({
                key: callback_tracker,
                value: {location, status},
              }),
            );
          });
        } else {
          //Permission Denied
          console.log('Permission Denied');
          webViewRef.current.postMessage(
            JSON.stringify({key: callback_tracker, value: {status}}),
          );
        }
      } catch (error) {
        console.log('error', error);
        webViewRef.current.postMessage(
          JSON.stringify({key: callback_tracker, value: {error}}),
        );
      }
    }
  };
  const getLocation = (callback: any) => {
    console.log('Getting Location ...');
    Geolocation.getCurrentPosition(
      position => {
        console.log('You are Here', position);
        return callback(position);
      },
      error => {
        console.log('error', error.message);
        return callback({});
      },
      {
        enableHighAccuracy: false,
        timeout: 30000,
        maximumAge: 1000,
      },
    );
  };
  const shareData = async (msg: string) => {
    try {
      await Share.share({
        message: '' + msg,
      });
    } catch (error: any) {
      alert('Error:' + error.message);
    }
  };

  const onShouldStartLoadWithRequest = (request: WebViewNavigation) => {
    const {url} = request;

    if (
      url.startsWith('tel:') ||
      url.startsWith('maps:') ||
      url.startsWith('mailto:') ||
      url.startsWith('geo:') ||
      url.startsWith('sms:')
    ) {
      Linking.openURL(url).catch(err => {
        Alert.alert('Failed to open Link: ' + err.message);
      });

      return false;
    }
    return true;
  };

  const jsCode = `
    if(typeof NativeAppListener!= 'undefined'){
      NativeAppListener.init();
    }
    true;
  `;
  console.log('webviwe url', process.env.WEB_URL);
  const sourceUri = process.env.WEB_URL || '';

  return (
    <>
      <Spinner visible={isLoading} color="#df1322" size="large" />
      <WebView
        ref={webViewRef}
        mixedContentMode="compatibility"
        originWhitelist={['*']}
        style={styles.webview}
        domStorageEnabled={true}
        cacheEnabled={true}
        source={{uri: sourceUri}}
        //renderLoading={LoadingIndicatorView}
        startInLoadingState={true}
        onNavigationStateChange={data => {
          console.log('navigation state go back', data.canGoBack);
          setCangoBack(data.canGoBack);
        }}
        onLoadProgress={({nativeEvent}) => {
          console.log('onLoadProgress go back', nativeEvent.canGoBack);
          setCangoBack(nativeEvent.canGoBack);
          setLoading(nativeEvent.progress !== 1);
        }}
        onShouldStartLoadWithRequest={onShouldStartLoadWithRequest}
        javaScriptEnabled={true}
        javaScriptEnabledAndroid={true}
        injectedJavaScript={jsCode}
        onMessage={event => {
          const data = JSON.parse(event.nativeEvent.data);
          switch (data.key) {
            case 'share':
              shareData(data.message);
              break;
            case 'location_access':
              locationAccess(data.message);
              break;
            case 'info':
              console.log('message info', data);
              break;
          }
        }}
      />
    </>
  );
}
const alert = (msg: string) =>
  Alert.alert(
    '', // TODO
    '' + msg,
    [
      {
        text: 'Cancel',
        onPress: () => console.log('Cancel Pressed'),
        style: 'cancel',
      },
      {text: 'OK', onPress: () => console.log('OK Pressed')},
    ],
  );
// function LoadingIndicatorView() {
//   return <ActivityIndicator color="#009b88" size="large" />;
// }
const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderWidth: 0,
    borderColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  webview: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
    borderColor: 'transparent',
  },
});

export default App;
